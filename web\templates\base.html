<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - 服务器监控系统</title>
    <link href="/static/css/output.css" rel="stylesheet">
</head>
<body>
    <header class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-4 shadow-lg">
        <div class="mx-auto px-4 max-w-7xl flex justify-between items-center">
            <div class="text-2xl font-bold">🖥️ 服务器监控系统</div>
            <nav>
                <ul class="flex space-x-8">
                    <li><a href="/" class="hover:text-gray-200 transition duration-300">首页</a></li>
                    <li><a href="/system" class="hover:text-gray-200 transition duration-300">系统监控</a></li>
                    <li><a href="/servers" class="hover:text-gray-200 transition duration-300">服务器管理</a></li>
                    <li><a href="/stats" class="hover:text-gray-200 transition duration-300">统计信息</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <main class="py-8 min-h-screen bg-gray-100">
        <div class="mx-auto px-4 max-w-7xl">
            {{template "content" .}}
        </div>
    </main>
    
    <footer class="bg-gray-800 text-white text-center py-4 mt-8">
        <div class="container mx-auto px-4">
            <p>&copy; 2025 服务器监控系统. All rights reserved.</p>
        </div>
    </footer>
    
    <script>
        // 自动刷新功能
        function autoRefresh() {
            if (window.location.pathname !== '/') {
                setTimeout(() => {
                    window.location.reload();
                }, 30000); // 30秒自动刷新
            }
        }
        
        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 获取进度条颜色类
        function getProgressClass(percentage) {
            if (percentage >= 90) return 'danger';
            if (percentage >= 70) return 'warning';
            return '';
        }
        
        // 页面加载完成后启动自动刷新
        document.addEventListener('DOMContentLoaded', autoRefresh);
    </script>
</body>
</html>
