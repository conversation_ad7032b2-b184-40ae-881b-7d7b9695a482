{{define "card"}}
<div class="bg-white p-6 rounded-lg shadow-md">
    {{if .Title}}
    <h2 class="text-xl font-semibold text-gray-700 mb-4">{{.Title}}</h2>
    {{end}}
    {{if .Content}}
    <p class="text-gray-600">{{.Content}}</p>
    {{end}}
    {{if .Body}}
    {{template "card-body" .Body}}
    {{end}}
</div>
{{end}}

{{define "card-body"}}
    <!-- This block is meant to be overridden by specific card implementations -->
    <!-- Example: {{template "card" (dict "Title" "My Card" "Body" (dict "Data" "Some data"))}} -->
    <!-- In the calling template: {{define "card-body"}}{{.Data}}{{end}} -->
{{end}}