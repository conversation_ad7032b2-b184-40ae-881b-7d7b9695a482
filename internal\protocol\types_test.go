package protocol

import (
	"encoding/json"
	"server-monitor/internal/crypto"
	"testing"
	"time"
)

func TestNewApplicationMessage(t *testing.T) {
	data := &AuthData{
		Username:  "test_user",
		Password:  "test_pass",
		ClientID:  "client_001",
		Version:   "1.0.0",
		Timestamp: time.Now().Unix(),
	}

	msg := NewApplicationMessage(MessageTypeAuth, "client", "server", data)

	if msg.ID == "" {
		t.Error("Message ID should not be empty")
	}

	if msg.Type != MessageTypeAuth {
		t.<PERSON>rf("Expected message type %s, got %s", MessageTypeAuth, msg.Type)
	}

	if msg.From != "client" {
		t.<PERSON>rf("Expected from 'client', got '%s'", msg.From)
	}

	if msg.To != "server" {
		t.Errorf("Expected to 'server', got '%s'", msg.To)
	}

	if msg.Timestamp <= 0 {
		t.<PERSON>rror("Message timestamp should be positive")
	}

	if msg.Data == nil {
		t.Error("Message data should not be nil")
	}
}

func TestApplicationMessageToJSON(t *testing.T) {
	data := &HeartbeatData{
		ClientID:  "client_001",
		Timestamp: time.Now().Unix(),
		Status:    "active",
	}

	msg := NewApplicationMessage(MessageTypeHeartbeat, "client", "server", data)

	jsonData, err := msg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to convert message to JSON: %v", err)
	}

	if len(jsonData) == 0 {
		t.Error("JSON data should not be empty")
	}

	// 验证是否是有效的 JSON (Verify if it's valid JSON)
	var temp map[string]interface{}
	if err := json.Unmarshal(jsonData, &temp); err != nil {
		t.Errorf("Generated JSON is invalid: %v", err)
	}
}

func TestApplicationMessageFromJSON(t *testing.T) {
	originalData := &TestRequestData{
		TestID:       "test_001",
		ServerName:   "server-01",
		ServerIP:     "*************",
		ServerPort:   5201,
		Duration:     30,
		Parallel:     4,
		Protocol:     "tcp",
		Reverse:      false,
		Bidir:        false,
		WindowSize:   "64K",
		BufferLength: "128K",
		Bandwidth:    "0",
	}

	originalMsg := NewApplicationMessage(MessageTypeTestRequest, "client", "server", originalData)

	// Convert to JSON
	jsonData, err := originalMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to convert message to JSON: %v", err)
	}

	// Convert back from JSON
	parsedMsg, err := FromJSON(jsonData)
	if err != nil {
		t.Fatalf("Failed to parse message from JSON: %v", err)
	}

	// 验证基本字段 (Verify basic fields)
	if parsedMsg.ID != originalMsg.ID {
		t.Errorf("Expected ID %s, got %s", originalMsg.ID, parsedMsg.ID)
	}

	if parsedMsg.Type != originalMsg.Type {
		t.Errorf("Expected type %s, got %s", originalMsg.Type, parsedMsg.Type)
	}

	if parsedMsg.From != originalMsg.From {
		t.Errorf("Expected from %s, got %s", originalMsg.From, parsedMsg.From)
	}

	if parsedMsg.To != originalMsg.To {
		t.Errorf("Expected to %s, got %s", originalMsg.To, parsedMsg.To)
	}
}

func TestApplicationMessageValidate(t *testing.T) {
	tests := []struct {
		name    string
		msg     *ApplicationMessage
		wantErr bool
	}{
		{
			name: "valid message",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: false,
		},
		{
			name: "missing ID",
			msg: &ApplicationMessage{
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "missing type",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Timestamp: time.Now().Unix(),
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "missing from",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: time.Now().Unix(),
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
		{
			name: "invalid timestamp",
			msg: &ApplicationMessage{
				ID:        "test_id",
				Type:      MessageTypeAuth,
				Timestamp: 0,
				From:      "client",
				To:        "server",
				Data:      &AuthData{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.msg.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestApplicationMessageIsExpired(t *testing.T) {
	// 创建一个带有当前时间戳的消息 (Create a message with the current timestamp)
	msg := &ApplicationMessage{
		Timestamp: time.Now().Unix(),
	}

	// 在 1 小时 TTL 内不应过期 (Should not expire within 1 hour TTL)
	if msg.IsExpired(time.Hour) {
		t.Error("Message should not be expired")
	}

	// 创建一个旧消息 (Create an old message)
	oldMsg := &ApplicationMessage{
		Timestamp: time.Now().Add(-2 * time.Hour).Unix(),
	}

	// 在 1 小时 TTL 后应过期 (Should expire after 1 hour TTL)
	if !oldMsg.IsExpired(time.Hour) {
		t.Error("Old message should be expired")
	}
}

func TestApplicationMessageGetDataAs(t *testing.T) {
	originalData := &AuthData{
		Username:  "test_user",
		Password:  "test_pass",
		ClientID:  "client_001",
		Version:   "1.0.0",
		Timestamp: time.Now().Unix(),
	}

	msg := NewApplicationMessage(MessageTypeAuth, "client", "server", originalData)

	// 将数据提取为 AuthData (Extract data as AuthData)
	var extractedData AuthData
	if err := msg.GetDataAs(&extractedData); err != nil {
		t.Fatalf("Failed to extract data: %v", err)
	}

	// 验证提取的数据 (Verify extracted data)
	if extractedData.Username != originalData.Username {
		t.Errorf("Expected username %s, got %s", originalData.Username, extractedData.Username)
	}

	if extractedData.ClientID != originalData.ClientID {
		t.Errorf("Expected client ID %s, got %s", originalData.ClientID, extractedData.ClientID)
	}
}

func TestCreateAuthMessage(t *testing.T) {
	msg := CreateAuthMessage("client", "server", "user", "pass", "client_001", "1.0.0")

	if msg.Type != MessageTypeAuth {
		t.Errorf("Expected message type %s, got %s", MessageTypeAuth, msg.Type)
	}

	var authData AuthData
	if err := msg.GetDataAs(&authData); err != nil {
		t.Fatalf("Failed to extract auth data: %v", err)
	}

	if authData.Username != "user" {
		t.Errorf("Expected username 'user', got '%s'", authData.Username)
	}

	if authData.Password != "pass" {
		t.Errorf("Expected password 'pass', got '%s'", authData.Password)
	}

	if authData.ClientID != "client_001" {
		t.Errorf("Expected client ID 'client_001', got '%s'", authData.ClientID)
	}
}

func TestCreateAuthResponse(t *testing.T) {
	expiresAt := time.Now().Add(time.Hour).Unix()
	msg := CreateAuthResponse("server", "client", true, "success", "token123", expiresAt)

	if msg.Type != MessageTypeAuthResponse {
		t.Errorf("Expected message type %s, got %s", MessageTypeAuthResponse, msg.Type)
	}

	var authResp AuthResponse
	if err := msg.GetDataAs(&authResp); err != nil {
		t.Fatalf("Failed to extract auth response: %v", err)
	}

	if !authResp.Success {
		t.Error("Expected success to be true")
	}

	if authResp.Message != "success" {
		t.Errorf("Expected message 'success', got '%s'", authResp.Message)
	}

	if authResp.Token != "token123" {
		t.Errorf("Expected token 'token123', got '%s'", authResp.Token)
	}

	if authResp.ExpiresAt != expiresAt {
		t.Errorf("Expected expires at %d, got %d", expiresAt, authResp.ExpiresAt)
	}
}

func TestCreateHeartbeat(t *testing.T) {
	msg := CreateHeartbeat("client", "server", "client_001", "active")

	if msg.Type != MessageTypeHeartbeat {
		t.Errorf("Expected message type %s, got %s", MessageTypeHeartbeat, msg.Type)
	}

	var heartbeat HeartbeatData
	if err := msg.GetDataAs(&heartbeat); err != nil {
		t.Fatalf("Failed to extract heartbeat data: %v", err)
	}

	if heartbeat.ClientID != "client_001" {
		t.Errorf("Expected client ID 'client_001', got '%s'", heartbeat.ClientID)
	}

	if heartbeat.Status != "active" {
		t.Errorf("Expected status 'active', got '%s'", heartbeat.Status)
	}
}

func TestCreateErrorMessage(t *testing.T) {
	msg := CreateErrorMessage("server", "client", 500, "Internal Error", "Database connection failed")

	if msg.Type != MessageTypeError {
		t.Errorf("Expected message type %s, got %s", MessageTypeError, msg.Type)
	}

	var errorData ErrorData
	if err := msg.GetDataAs(&errorData); err != nil {
		t.Fatalf("Failed to extract error data: %v", err)
	}

	if errorData.Code != 500 {
		t.Errorf("Expected error code 500, got %d", errorData.Code)
	}

	if errorData.Message != "Internal Error" {
		t.Errorf("Expected error message 'Internal Error', got '%s'", errorData.Message)
	}

	if errorData.Details != "Database connection failed" {
		t.Errorf("Expected error details 'Database connection failed', got '%s'", errorData.Details)
	}
}

func TestApplicationMessageValidateWithReplayProtection(t *testing.T) {
	// 初始化全局重放攻击防护器，使用更宽松的时间窗口 (Initialize global replay protector with more lenient time window)
	config := &crypto.ReplayProtectorConfig{
		TimeWindow:         10 * time.Minute, // 更长的时间窗口 (Longer time window)
		CacheSize:          1000,
		ClockSkewTolerance: time.Minute,
		EnableCleanup:      false,
	}
	err := crypto.InitGlobalReplayProtector(config)
	if err != nil {
		t.Fatalf("Failed to initialize global replay protector: %v", err)
	}

	// 测试有效消息 (Test valid message)
	msg := NewApplicationMessage(MessageTypeAuth, "client1", "server1", AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 更新消息时间戳为当前时间 (Update message timestamp to current time)
	msg.Timestamp = time.Now().UnixNano()

	// 第一次验证应该成功 (First validation should succeed)
	err = msg.ValidateWithReplayProtection()
	if err != nil {
		t.Errorf("First validation should succeed: %v", err)
	}

	// 第二次验证应该检测到重放攻击 (Second validation should detect replay attack)
	err = msg.ValidateWithReplayProtection()
	if err == nil {
		t.Error("Second validation should detect replay attack")
	}

	// 测试无效消息 (Test invalid message)
	invalidMsg := &ApplicationMessage{
		ID:        "",
		Type:      MessageTypeAuth,
		From:      "client1",
		To:        "server1",
		Timestamp: 0,
	}

	err = invalidMsg.ValidateWithReplayProtection()
	if err == nil {
		t.Error("Invalid message should fail validation")
	}
}
