// websocket.js
console.log('WebSocket script loaded.');

let ws;
const messageHandlers = {}; // To store handlers for different message types

function connectWebSocket(path, onOpenCallback, onCloseCallback, onErrorCallback) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const url = `${protocol}//${host}${path}`;

    ws = new WebSocket(url);

    ws.onopen = function(event) {
        console.log('WebSocket connected:', event);
        if (onOpenCallback) onOpenCallback(event);
    };

    ws.onmessage = function(event) {
        try {
            const message = JSON.parse(event.data);
            if (message.type && messageHandlers[message.type]) {
                messageHandlers[message.type](message.payload);
            } else {
                console.log('Received unknown WebSocket message:', message);
            }
        } catch (e) {
            console.error('Failed to parse WebSocket message:', e, event.data);
        }
    };

    ws.onclose = function(event) {
        console.log('WebSocket disconnected:', event);
        if (onCloseCallback) onCloseCallback(event);
        // Attempt to reconnect after a delay
        setTimeout(() => connectWebSocket(path, onOpenCallback, onCloseCallback, onErrorCallback), 3000);
    };

    ws.onerror = function(error) {
        console.error('WebSocket error:', error);
        if (onErrorCallback) onErrorCallback(error);
        ws.close(); // Close to trigger reconnect logic
    };
}

function sendMessage(type, payload) {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type, payload }));
    } else {
        console.warn('WebSocket not open. Message not sent:', { type, payload });
    }
}

function registerMessageHandler(type, handler) {
    messageHandlers[type] = handler;
}

function unregisterMessageHandler(type) {
    delete messageHandlers[type];
}

export { connectWebSocket, sendMessage, registerMessageHandler, unregisterMessageHandler };