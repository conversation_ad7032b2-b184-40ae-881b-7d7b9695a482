{{define "content"}}
<h1 class="text-3xl font-bold text-gray-800 mb-6">服务管理</h1>

<div class="bg-white p-6 rounded-lg shadow-md mb-6">
    <h2 class="text-xl font-semibold text-gray-700 mb-4">服务列表</h2>
    <table class="min-w-full bg-white">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">服务名称</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">状态</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">操作</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">Web Server</td>
                <td class="py-2 px-4 border-b border-gray-200 text-green-500">运行中</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs">停止</button>
                    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs ml-2">重启</button>
                </td>
            </tr>
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">Database</td>
                <td class="py-2 px-4 border-b border-gray-200 text-green-500">运行中</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs">停止</button>
                    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs ml-2">重启</button>
                </td>
            </tr>
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">Monitoring Agent</td>
                <td class="py-2 px-4 border-b border-gray-200 text-red-500">已停止</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs">启动</button>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold text-gray-700 mb-4">添加新服务</h2>
    <form>
        <div class="mb-4">
            <label for="serviceName" class="block text-gray-700 text-sm font-bold mb-2">服务名称:</label>
            <input type="text" id="serviceName" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        <div class="mb-4">
            <label for="serviceCommand" class="block text-gray-700 text-sm font-bold mb-2">启动命令:</label>
            <input type="text" id="serviceCommand" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        <button type="submit" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">添加服务</button>
    </form>
</div>
{{end}}