const { test, expect } = require('@playwright/test');

test('Web UI should be accessible and display correct title', async ({ page }) => {
  // Navigate to the home page of the web UI
  await page.goto('http://localhost:8080/');

  // Expect a title "服务器监控系统"
  await expect(page).toHaveTitle('服务器监控系统');

  // You can add more assertions here to check for specific content on the page
  // For example, check if a specific heading is visible
  await expect(page.locator('h1')).toContainText('服务器监控系统');
});