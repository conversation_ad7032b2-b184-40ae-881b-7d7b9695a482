// charts.js
// This file will contain common chart utility functions or configurations if needed.
// For now, the chart logic is directly in server-detail.html as per the previous implementation.

console.log('Charts script loaded.');

// Example function (can be expanded later)
function createLine<PERSON>hart(ctx, data, options) {
    new Chart(ctx, {
        type: 'line',
        data: data,
        options: options
    });
}

function createBar<PERSON>hart(ctx, data, options) {
    new Chart(ctx, {
        type: 'bar',
        data: data,
        options: options
    });
}