package protocol

import (
	"encoding/json"
	"fmt"
	"server-monitor/internal/crypto"
	"time"
)

// MessageType 应用程序消息类型 (Application message type)
type MessageType string

const (
	// 认证相关消息 (Authentication related messages)
	MessageTypeAuth         MessageType = "auth"
	MessageTypeAuthResponse MessageType = "auth_response"

	// 心跳消息 (Heartbeat messages)
	MessageTypeHeartbeat         MessageType = "heartbeat"
	MessageTypeHeartbeatResponse MessageType = "heartbeat_response"

	// 系统信息消息 (System information messages)
	MessageTypeSystemInfo         MessageType = "system_info"
	MessageTypeSystemInfoResponse MessageType = "system_info_response"

	// 测试相关消息 (Test related messages)
	MessageTypeTestRequest  MessageType = "test_request"
	MessageTypeTestResponse MessageType = "test_response"
	MessageTypeTestResult   MessageType = "test_result"

	// 配置相关消息 (Configuration related messages)
	MessageTypeConfigUpdate         MessageType = "config_update"
	MessageTypeConfigUpdateResponse MessageType = "config_update_response"

	// 数据同步消息 (Data synchronization messages)
	MessageTypeDataSync         MessageType = "data_sync"
	MessageTypeDataSyncResponse MessageType = "data_sync_response"

	// 错误消息类型 (Error message type)
	MessageTypeError MessageType = "error"
)

// ApplicationMessage 应用程序消息结构 (Application message structure)
type ApplicationMessage struct {
	ID        string      `json:"id"`        // 消息唯一标识符 (Unique message identifier)
	Type      MessageType `json:"type"`      // 消息的类型 (Type of the message)
	Timestamp int64       `json:"timestamp"` // 消息创建时的时间戳 (Timestamp when the message was created)
	From      string      `json:"from"`      // 消息发送方标识 (Identifier of the message sender)
	To        string      `json:"to"`        // 消息接收方标识 (Identifier of the message receiver)
	Data      interface{} `json:"data"`      // 消息携带的数据 (Data carried by the message)
}

// AuthData 认证数据结构 (Authentication data structure)
type AuthData struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	ClientID  string `json:"client_id"`
	Version   string `json:"version"`
	Timestamp int64  `json:"timestamp"`
}

// AuthResponse 认证响应数据结构 (Authentication response data structure)
type AuthResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Token     string `json:"token,omitempty"`
	ExpiresAt int64  `json:"expires_at,omitempty"`
}

// HeartbeatData 心跳数据结构 (Heartbeat data structure)
type HeartbeatData struct {
	ClientID  string `json:"client_id"`
	Timestamp int64  `json:"timestamp"`
	Status    string `json:"status"`
}

// SystemInfoData 系统信息数据结构 (System information data structure)
type SystemInfoData struct {
	Hostname     string  `json:"hostname"`
	OS           string  `json:"os"`
	Architecture string  `json:"architecture"`
	CPUUsage     float64 `json:"cpu_usage"`
	MemoryUsage  float64 `json:"memory_usage"`
	DiskUsage    float64 `json:"disk_usage"`
	NetworkRx    uint64  `json:"network_rx"`
	NetworkTx    uint64  `json:"network_tx"`
	Uptime       int64   `json:"uptime"`
	LoadAverage  float64 `json:"load_average"`
}

// TestRequestData 测试请求数据结构 (Test request data structure)
type TestRequestData struct {
	TestID       string `json:"test_id"`
	ServerName   string `json:"server_name"`
	ServerIP     string `json:"server_ip"`
	ServerPort   int    `json:"server_port"`
	Duration     int    `json:"duration"`      // 测试持续时间（秒） (Test duration (seconds))
	Parallel     int    `json:"parallel"`      // 并行连接数 (Number of parallel connections)
	Protocol     string `json:"protocol"`      // 测试协议类型（tcp/udp） (Test protocol type (tcp/udp))
	Reverse      bool   `json:"reverse"`       // 是否进行反向测试 (Whether to perform reverse test)
	Bidir        bool   `json:"bidir"`         // 是否进行双向测试 (Whether to perform bidirectional test)
	WindowSize   string `json:"window_size"`   // TCP 窗口大小 (TCP window size)
	BufferLength string `json:"buffer_length"` // 缓冲区长度 (Buffer length)
	Bandwidth    string `json:"bandwidth"`     // 带宽限制 (Bandwidth limit)
}

// TestResponseData 测试响应数据结构 (Test response data structure)
type TestResponseData struct {
	TestID  string `json:"test_id"`
	Success bool   `json:"success"`
	Message string `json:"message"`
	StartAt int64  `json:"start_at,omitempty"`
}

// TestResultData 测试结果数据结构 (Test result data structure)
type TestResultData struct {
	TestID           string  `json:"test_id"`
	ServerName       string  `json:"server_name"`
	ServerIP         string  `json:"server_ip"`
	ServerPort       int     `json:"server_port"`
	Protocol         string  `json:"protocol"`
	Duration         float64 `json:"duration"`
	BytesSent        int64   `json:"bytes_sent"`
	BytesReceived    int64   `json:"bytes_received"`
	BitsPerSecond    float64 `json:"bits_per_second"`
	Jitter           float64 `json:"jitter,omitempty"`
	LostPackets      int     `json:"lost_packets,omitempty"`
	TotalPackets     int     `json:"total_packets,omitempty"`
	LostPercent      float64 `json:"lost_percent,omitempty"`
	Retransmits      int     `json:"retransmits,omitempty"`
	CongestionWindow int     `json:"congestion_window,omitempty"`
	StartTime        int64   `json:"start_time"`
	EndTime          int64   `json:"end_time"`
	Success          bool    `json:"success"`
	ErrorMessage     string  `json:"error_message,omitempty"`
}

// ErrorData 错误数据结构 (Error data structure)
type ErrorData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// NewApplicationMessage 创建一个新的应用程序消息实例 (Creates a new application message instance)
func NewApplicationMessage(msgType MessageType, from, to string, data interface{}) *ApplicationMessage {
	return &ApplicationMessage{
		ID:        generateMessageID(),
		Type:      msgType,
		Timestamp: time.Now().Unix(),
		From:      from,
		To:        to,
		Data:      data,
	}
}

// ToJSON 将应用程序消息编码为 JSON 字节数组 (Encodes the application message into a JSON byte array)
func (m *ApplicationMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON 从 JSON 字节数组解码应用程序消息 (Decodes an application message from a JSON byte array)
func FromJSON(data []byte) (*ApplicationMessage, error) {
	var msg ApplicationMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal message: %w", err)
	}
	return &msg, nil
}

// Validate 验证应用程序消息的必填字段 (Validates required fields of the application message)
func (m *ApplicationMessage) Validate() error {
	if m.ID == "" {
		return fmt.Errorf("message ID is required")
	}
	if m.Type == "" {
		return fmt.Errorf("message type is required")
	}
	if m.From == "" {
		return fmt.Errorf("message from is required")
	}
	if m.Timestamp <= 0 {
		return fmt.Errorf("message timestamp is required")
	}
	return nil
}

// IsExpired 检查应用程序消息是否已过期 (Checks if the application message has expired)
func (m *ApplicationMessage) IsExpired(ttl time.Duration) bool {
	msgTime := time.Unix(m.Timestamp, 0)
	return time.Since(msgTime) > ttl
}

// GetDataAs 将消息数据解码到指定的类型 (Decodes message data into the specified type)
func (m *ApplicationMessage) GetDataAs(target interface{}) error {
	dataBytes, err := json.Marshal(m.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal message data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, target); err != nil {
		return fmt.Errorf("failed to unmarshal message data: %w", err)
	}

	return nil
}

// CreateAuthMessage 创建一个认证请求消息 (Creates an authentication request message)
func CreateAuthMessage(from, to, username, password, clientID, version string) *ApplicationMessage {
	authData := &AuthData{
		Username:  username,
		Password:  password,
		ClientID:  clientID,
		Version:   version,
		Timestamp: time.Now().Unix(),
	}
	return NewApplicationMessage(MessageTypeAuth, from, to, authData)
}

// CreateAuthResponse 创建一个认证响应消息 (Creates an authentication response message)
func CreateAuthResponse(from, to string, success bool, message, token string, expiresAt int64) *ApplicationMessage {
	authResp := &AuthResponse{
		Success:   success,
		Message:   message,
		Token:     token,
		ExpiresAt: expiresAt,
	}
	return NewApplicationMessage(MessageTypeAuthResponse, from, to, authResp)
}

// CreateHeartbeat 创建一个心跳消息 (Creates a heartbeat message)
func CreateHeartbeat(from, to, clientID, status string) *ApplicationMessage {
	heartbeat := &HeartbeatData{
		ClientID:  clientID,
		Timestamp: time.Now().Unix(),
		Status:    status,
	}
	return NewApplicationMessage(MessageTypeHeartbeat, from, to, heartbeat)
}

// CreateTestRequest 创建一个测试请求消息 (Creates a test request message)
func CreateTestRequest(from, to string, testReq *TestRequestData) *ApplicationMessage {
	return NewApplicationMessage(MessageTypeTestRequest, from, to, testReq)
}

// CreateTestResult 创建一个测试结果消息 (Creates a test result message)
func CreateTestResult(from, to string, testResult *TestResultData) *ApplicationMessage {
	return NewApplicationMessage(MessageTypeTestResult, from, to, testResult)
}

// CreateErrorMessage 创建一个错误消息 (Creates an error message)
func CreateErrorMessage(from, to string, code int, message, details string) *ApplicationMessage {
	errorData := &ErrorData{
		Code:    code,
		Message: message,
		Details: details,
	}
	return NewApplicationMessage(MessageTypeError, from, to, errorData)
}

// generateMessageID 生成一个唯一的消息 ID (Generates a unique message ID)
func generateMessageID() string {
	return crypto.GenerateSecureMessageIDMust()
}
