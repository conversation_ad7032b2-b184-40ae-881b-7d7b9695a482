{{define "content"}}
<h1 class="text-3xl font-bold text-gray-800 mb-6">服务器详情: {{.Server.Name}}</h1>

<div class="bg-white p-6 rounded-lg shadow-md mb-6">
    <h2 class="text-xl font-semibold text-gray-700 mb-4">基本信息</h2>
    <div class="grid grid-cols-2 gap-4 text-gray-600">
        <div><strong>ID:</strong> {{.Server.ID}}</div>
        <div><strong>名称:</strong> {{.Server.Name}}</div>
        <div><strong>IP地址:</strong> {{.Server.IPAddress}}</div>
        <div><strong>端口:</strong> {{.Server.Port}}</div>
        <div><strong>操作系统:</strong> {{.Server.OS}}</div>
        <div><strong>状态:</strong> {{.Server.Status}}</div>
        <div><strong>最后更新:</strong> {{.Server.LastUpdated.Format "2006-01-02 15:04:05"}}</div>
    </div>
</div>

<div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold text-gray-700 mb-4">实时监控数据</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">CPU 使用率</h3>
            <canvas id="cpuChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">内存使用率</h3>
            <canvas id="memoryChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">磁盘使用率</h3>
            <canvas id="diskChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">网络流量</h3>
            <canvas id="networkChart"></canvas>
        </div>
    </div>
</div>

<script src="/static/js/chart.js"></script>
<script>
    // 示例数据，实际应通过WebSocket或API获取
    const cpuData = {
        labels: ['1s', '2s', '3s', '4s', '5s'],
        datasets: [{
            label: 'CPU Usage (%)',
            data: [65, 59, 80, 81, 56],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    };
    const memoryData = {
        labels: ['1s', '2s', '3s', '4s', '5s'],
        datasets: [{
            label: 'Memory Usage (%)',
            data: [28, 48, 40, 19, 86],
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1
        }]
    };
    const diskData = {
        labels: ['1s', '2s', '3s', '4s', '5s'],
        datasets: [{
            label: 'Disk Usage (%)',
            data: [70, 72, 75, 71, 73],
            borderColor: 'rgb(54, 162, 235)',
            tension: 0.1
        }]
    };
    const networkData = {
        labels: ['1s', '2s', '3s', '4s', '5s'],
        datasets: [{
            label: 'Network Traffic (KB/s)',
            data: [120, 150, 130, 180, 160],
            borderColor: 'rgb(255, 205, 86)',
            tension: 0.1
        }]
    };

    const config = {
        type: 'line',
        data: {},
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    };

    new Chart(
        document.getElementById('cpuChart'),
        { ...config, data: cpuData }
    );
    new Chart(
        document.getElementById('memoryChart'),
        { ...config, data: memoryData }
    );
    new Chart(
        document.getElementById('diskChart'),
        { ...config, data: diskData }
    );
    new Chart(
        document.getElementById('networkChart'),
        { ...config, data: networkData }
    );
</script>
{{end}}