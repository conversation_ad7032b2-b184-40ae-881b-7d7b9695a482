{{define "content"}}
<h1 class="text-3xl font-bold text-gray-800 mb-6">服务器详情: {{.Server.Name}}</h1>

<div class="bg-white p-6 rounded-lg shadow-md mb-6">
    <h2 class="text-xl font-semibold text-gray-700 mb-4">基本信息</h2>
    <div class="grid grid-cols-2 gap-4 text-gray-600">
        <div><strong>ID:</strong> {{.Server.ID}}</div>
        <div><strong>名称:</strong> {{.Server.Name}}</div>
        <div><strong>IP地址:</strong> {{.Server.IPAddress}}</div>
        <div><strong>端口:</strong> {{.Server.Port}}</div>
        <div><strong>操作系统:</strong> {{.Server.OS}}</div>
        <div><strong>状态:</strong> {{.Server.Status}}</div>
        <div><strong>最后更新:</strong> {{.Server.LastUpdated.Format "2006-01-02 15:04:05"}}</div>
    </div>
</div>

<div class="bg-white p-6 rounded-lg shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-700">实时监控数据</h2>
        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
            刷新数据
        </button>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">CPU 使用率</h3>
            <canvas id="cpuChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">内存使用率</h3>
            <canvas id="memoryChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">磁盘使用率</h3>
            <canvas id="diskChart"></canvas>
        </div>
        <div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">网络流量</h3>
            <canvas id="networkChart"></canvas>
        </div>
    </div>
</div>

<!-- 加载Chart.js库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.5.0/dist/chart.min.js"></script>
<!-- 加载自定义图表工具库 -->
<script src="/static/js/charts.js"></script>
<!-- 加载WebSocket客户端 -->
<script src="/static/js/websocket.js" type="module"></script>

<script type="module">
    import { initChartWebSocket, cleanupChartWebSocket } from '/static/js/websocket.js';

    // 图表配置
    const chartConfigs = {
        'cpuChart': 'cpu',
        'memoryChart': 'memory',
        'diskChart': 'disk',
        'networkChart': 'network'
    };

    // 页面加载完成后初始化图表和WebSocket连接
    document.addEventListener('DOMContentLoaded', function() {
        console.log('初始化服务器详情页面图表...');

        // 初始化图表WebSocket连接
        initChartWebSocket('/ws', chartConfigs);

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            cleanupChartWebSocket();
        });

        // 添加手动刷新按钮功能
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                // 重置所有图表数据
                Object.keys(chartConfigs).forEach(canvasId => {
                    if (window.ChartManager) {
                        window.ChartManager.resetChartData(canvasId);
                    }
                });

                // 请求新的系统信息
                import('/static/js/websocket.js').then(module => {
                    module.requestSystemInfo();
                });
            });
        }
    });
</script>
{{end}}