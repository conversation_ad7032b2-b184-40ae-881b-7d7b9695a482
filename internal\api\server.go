package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/database"
	"server-monitor/internal/monitor"
	"server-monitor/internal/models"

	"github.com/gin-gonic/gin"
)

// Server API服务器
type Server struct {
	config        *config.Config
	repository    database.Repository
	systemMonitor *monitor.SystemMonitor
	httpServer    *http.Server
	router        *gin.Engine
}

// NewServer 创建新的API服务器
func NewServer(cfg *config.Config, repo database.Repository, sysMon *monitor.SystemMonitor) *Server {
	// 设置Gin模式
	if cfg.System.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	server := &Server{
		config:        cfg,
		repository:    repo,
		systemMonitor: sysMon,
		router:        router,
	}

	// 设置路由
	server.setupRoutes()

	return server
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// API版本前缀
	v1 := s.router.Group(s.config.API.Prefix)

	// 健康检查
	v1.GET("/health", s.healthCheck)

	// 系统信息相关
	system := v1.Group("/system")
	{
		system.GET("/info", s.getSystemInfo)
		system.GET("/cpu", s.getCPUStats)
		system.GET("/memory", s.getMemoryInfo)
		system.GET("/disk", s.getDiskStats)
		system.GET("/network", s.getNetworkStats)
	}

	// 服务器管理
	servers := v1.Group("/servers")
	{
		servers.GET("", s.listServers)
		servers.POST("", s.createServer)
		servers.GET("/:id", s.getServer)
		servers.PUT("/:id", s.updateServer)
		servers.DELETE("/:id", s.deleteServer)
	}

	// 测试结果 (暂时移除，等待Repository接口支持)
	// tests := v1.Group("/tests")
	// {
	// 	tests.GET("", s.listTestResults)
	// 	tests.POST("", s.createTestResult)
	// 	tests.GET("/:id", s.getTestResult)
	// 	tests.DELETE("/:id", s.deleteTestResult)
	// }

	// 统计信息
	stats := v1.Group("/stats")
	{
		stats.GET("/summary", s.getStatsSummary)
		stats.GET("/servers", s.getServerStats)
		// stats.GET("/tests", s.getTestStats) // 暂时移除
	}
}

// Start 启动API服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.API.Host, s.config.API.Port)

	s.httpServer = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  s.config.API.Timeout,
		WriteTimeout: s.config.API.Timeout,
		IdleTimeout:  time.Minute,
	}

	fmt.Printf("API server starting on %s\n", addr)

	if s.config.API.TLS.Enabled {
		return s.httpServer.ListenAndServeTLS(
			s.config.API.TLS.CertFile,
			s.config.API.TLS.KeyFile,
		)
	}

	return s.httpServer.ListenAndServe()
}

// Stop 停止API服务器
func (s *Server) Stop(ctx context.Context) error {
	if s.httpServer == nil {
		return nil
	}

	fmt.Println("Stopping API server...")
	return s.httpServer.Shutdown(ctx)
}

// Response 通用响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// successResponse 成功响应
func successResponse(data interface{}) Response {
	return Response{
		Success: true,
		Data:    data,
	}
}

// errorResponse 错误响应
func errorResponse(message string) Response {
	return Response{
		Success: false,
		Error:   message,
	}
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, successResponse(gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"version":   s.config.System.Version,
	}))
}

// getSystemInfo 获取系统信息
func (s *Server) getSystemInfo(c *gin.Context) {
	info, err := s.systemMonitor.GetSystemInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(info))
}

// getCPUStats 获取CPU统计
func (s *Server) getCPUStats(c *gin.Context) {
	stats, err := s.systemMonitor.GetCPUStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// getMemoryInfo 获取内存信息
func (s *Server) getMemoryInfo(c *gin.Context) {
	memInfo, err := monitor.GetMemoryInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	swapInfo, err := monitor.GetSwapInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"virtual": memInfo,
		"swap":    swapInfo,
	}))
}

// getDiskStats 获取磁盘统计
func (s *Server) getDiskStats(c *gin.Context) {
	stats, err := s.systemMonitor.GetDiskStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// getNetworkStats 获取网络统计
func (s *Server) getNetworkStats(c *gin.Context) {
	stats, err := s.systemMonitor.GetNetworkStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(stats))
}

// listServers 列出服务器
func (s *Server) listServers(c *gin.Context) {
	activeOnly := c.Query("active") == "true"

	servers, err := s.repository.ListServers(activeOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(servers))
}

// createServer 创建服务器
func (s *Server) createServer(c *gin.Context) {
	var server models.Server // Change to models.Server
	if err := c.ShouldBindJSON(&server); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse(err.Error()))
		return
	}

	if err := s.repository.CreateServer(&server); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusCreated, successResponse(server))
}

// getServer 获取服务器
func (s *Server) getServer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid server ID"))
		return
	}

	server, err := s.repository.GetServer(id)
	if err != nil {
		c.JSON(http.StatusNotFound, errorResponse("Server not found"))
		return
	}

	c.JSON(http.StatusOK, successResponse(server))
}

// updateServer 更新服务器
func (s *Server) updateServer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid server ID"))
		return
	}

	var server models.Server // Change to models.Server
	if err := c.ShouldBindJSON(&server); err != nil {
		c.JSON(http.StatusBadRequest, errorResponse(err.Error()))
		return
	}

	server.ID = uint(id) // Cast id to uint
	if err := s.repository.UpdateServer(&server); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(server))
}

// deleteServer 删除服务器
func (s *Server) deleteServer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errorResponse("Invalid server ID"))
		return
	}

	if err := s.repository.DeleteServer(id); err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{"message": "Server deleted successfully"}))
}

// TestResult相关方法暂时移除，等待Repository接口支持
// TODO: 实现TestResult相关的API方法

// getStatsSummary 获取统计摘要
func (s *Server) getStatsSummary(c *gin.Context) {
	// 获取服务器数量
	servers, err := s.repository.ListServers(false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	// 计算统计信息
	activeServers := 0
	for _, server := range servers {
		if server.IsActive {
			activeServers++
		}
	}

	summary := gin.H{
		"servers": gin.H{
			"total":  len(servers),
			"active": activeServers,
		},
		"timestamp": time.Now().Unix(),
	}

	c.JSON(http.StatusOK, successResponse(summary))
}

// getServerStats 获取服务器统计
func (s *Server) getServerStats(c *gin.Context) {
	servers, err := s.repository.ListServers(false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errorResponse(err.Error()))
		return
	}

	c.JSON(http.StatusOK, successResponse(gin.H{
		"servers": servers,
		"count":   len(servers),
	}))
}

// getTestStats 获取测试统计 (暂时移除，等待Repository接口支持)
// TODO: 实现测试统计API
