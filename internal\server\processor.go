package server

import (
	"context"
	"fmt"
	"log"
	"time" // Add time package

	"server-monitor/internal/database"
	"server-monitor/internal/models"
)

// DataProcessor defines the interface for processing incoming data.
type DataProcessor interface {
	ProcessData(ctx context.Context, data *models.SystemInfo) error
}

// processor implements the DataProcessor interface.
type processor struct {
	dbRepo database.Repository // Assuming a generic repository interface
}

// NewDataProcessor creates a new data processor.
func NewDataProcessor(dbRepo database.Repository) DataProcessor {
	return &processor{
		dbRepo: dbRepo,
	}
}

// ProcessData processes incoming system information data.
func (p *processor) ProcessData(ctx context.Context, data *models.SystemInfo) error {
	log.Printf("Processing data for server %d: CPU %.2f%%, Mem %.2f%%", data.ServerID, data.CPUUsage, data.MemoryUsage)

	// 1. 数据存储逻辑
	if err := p.dbRepo.CreateSystemInfo(data); err != nil {
		return fmt.Errorf("failed to store system info: %w", err)
	}
	log.Printf("System info for server %d stored successfully.", data.ServerID)

	// 2. 告警检测
	// 示例：CPU使用率超过90%触发告警
	if data.CPUUsage > 90.0 {
		alert := &models.Alert{
			ServerID:  data.ServerID,
			Metric:    "cpu_usage",
			Threshold: 90.0,
			Operator:  ">",
			Value:     data.CPUUsage,
			Message:   fmt.Sprintf("CPU usage exceeded 90%%: %.2f%%", data.CPUUsage),
			Level:     "critical",
			Status:    "active",
			Timestamp: time.Now(),
		}
		if err := p.dbRepo.CreateAlert(alert); err != nil {
			log.Printf("Failed to create CPU usage alert: %v", err)
		} else {
			log.Printf("ALERT: High CPU usage for server %d: %.2f%%. Alert ID: %d", data.ServerID, data.CPUUsage, alert.ID)
		}
	}

	// 示例：内存使用率超过80%触发告警
	if data.MemoryUsage > 80.0 {
		alert := &models.Alert{
			ServerID:  data.ServerID,
			Metric:    "memory_usage",
			Threshold: 80.0,
			Operator:  ">",
			Value:     data.MemoryUsage,
			Message:   fmt.Sprintf("Memory usage exceeded 80%%: %.2f%%", data.MemoryUsage),
			Level:     "warning",
			Status:    "active",
			Timestamp: time.Now(),
		}
		if err := p.dbRepo.CreateAlert(alert); err != nil {
			log.Printf("Failed to create Memory usage alert: %v", err)
		} else {
			log.Printf("ALERT: High Memory usage for server %d: %.2f%%. Alert ID: %d", data.ServerID, data.MemoryUsage, alert.ID)
		}
	}

	// 3. 数据聚合 (Placeholder)
	// 暂时保持占位符，后续可以根据需求实现更复杂的数据聚合逻辑
	log.Printf("Data aggregation for server %d completed (placeholder).", data.ServerID)

	return nil
}
