package security

import (
	"context"
	"sync"
	"testing"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/crypto"
	"server-monitor/internal/protocol"
)

// SecurityTestSuite 安全特性集成测试套件 (Security feature integration test suite)
type SecurityTestSuite struct {
	config          *config.Config
	keyManager      *crypto.KeyManager
	encryptionSvc   *crypto.EncryptionService
	reliabilityMgr  *protocol.MessageReliabilityManager
	validator       *protocol.MessageValidator
	replayProtector *crypto.ReplayProtector
}

// NewSecurityTestSuite 创建新的安全测试套件 (Creates a new security test suite)
func NewSecurityTestSuite(t *testing.T) *SecurityTestSuite {
	// 创建测试配置 (Create test configuration)
	manager := config.NewManager("")
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	cfg := manager.Get()
	
	// 设置测试用的安全配置 (Set security config for testing)
	cfg.Security.KeyManager.MasterPassword = "test-master-password-for-integration"
	cfg.Security.KeyManager.RotationInterval = 100 * time.Millisecond // 快速轮换用于测试 (Fast rotation for testing)
	cfg.Security.KeyManager.KeyTTL = time.Hour
	cfg.Security.KeyManager.AutoRotate = true
	
	// 创建密钥管理器 (Create key manager)
	kmConfig := &crypto.KeyManagerConfig{
		RotationInterval: cfg.Security.KeyManager.RotationInterval,
		KeyTTL:          cfg.Security.KeyManager.KeyTTL,
		MaxKeyHistory:   cfg.Security.KeyManager.MaxKeyHistory,
		AutoRotate:      cfg.Security.KeyManager.AutoRotate,
		MasterPassword:  []byte(cfg.Security.KeyManager.MasterPassword),
	}
	
	keyManager, err := crypto.NewKeyManager(kmConfig)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}
	
	// 创建加密服务 (Create encryption service)
	encryptionSvc := crypto.NewEncryptionService(keyManager)
	
	// 创建消息验证器 (Create message validator)
	validatorConfig := protocol.DefaultValidatorConfig()
	validator := protocol.NewMessageValidator(validatorConfig)
	
	// 创建重放攻击防护器 (Create replay protector)
	replayConfig := &crypto.ReplayProtectorConfig{
		TimeWindow: 5 * time.Minute,
		CacheSize:  1000,
		CacheTTL:   10 * time.Minute,
	}
	replayProtector := crypto.NewReplayProtector(replayConfig)
	
	// 创建模拟消息发送器 (Create mock message sender)
	mockSender := &MockSecureMessageSender{
		encryptionSvc: encryptionSvc,
		validator:     validator,
	}
	
	// 创建消息可靠性管理器 (Create message reliability manager)
	reliabilityConfig := protocol.DefaultMessageReliabilityConfig()
	reliabilityConfig.AckTimeout = 100 * time.Millisecond // 短超时用于测试 (Short timeout for testing)
	reliabilityMgr := protocol.NewMessageReliabilityManager(reliabilityConfig, mockSender)
	
	return &SecurityTestSuite{
		config:          cfg,
		keyManager:      keyManager,
		encryptionSvc:   encryptionSvc,
		reliabilityMgr:  reliabilityMgr,
		validator:       validator,
		replayProtector: replayProtector,
	}
}

// Cleanup 清理测试资源 (Cleanup test resources)
func (s *SecurityTestSuite) Cleanup() {
	if s.keyManager != nil {
		s.keyManager.Stop()
	}
	if s.reliabilityMgr != nil {
		s.reliabilityMgr.Stop()
	}
}

// MockSecureMessageSender 模拟安全消息发送器 (Mock secure message sender)
type MockSecureMessageSender struct {
	encryptionSvc *crypto.EncryptionService
	validator     *protocol.MessageValidator
	sentMessages  []string
	mutex         sync.Mutex
}

// SendMessage 发送消息 (Sends message)
func (m *MockSecureMessageSender) SendMessage(ctx context.Context, msg *protocol.ApplicationMessage) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	// 验证消息 (Validate message)
	if err := m.validator.ValidateMessage(msg); err != nil {
		return err
	}
	
	// 模拟发送 (Simulate sending)
	m.sentMessages = append(m.sentMessages, msg.ID)
	return nil
}

// GetSentMessages 获取已发送消息 (Gets sent messages)
func (m *MockSecureMessageSender) GetSentMessages() []string {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	result := make([]string, len(m.sentMessages))
	copy(result, m.sentMessages)
	return result
}

// TestSecureMessageFlow 测试完整的安全消息流程 (Tests complete secure message flow)
func TestSecureMessageFlow(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()
	
	// 创建测试消息 (Create test message)
	msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	
	// 设置时间戳 (Set timestamp)
	msg.Timestamp = time.Now().UnixNano()
	
	// 生成序列号 (Generate sequence number)
	msg.GenerateAndSetSequenceNumber()
	
	// 生成校验和 (Generate checksum)
	msg.GenerateAndSetChecksum()
	
	// 验证消息完整性 (Validate message integrity)
	err := msg.ValidateWithIntegrity()
	if err != nil {
		t.Fatalf("Message integrity validation failed: %v", err)
	}
	
	// 加密消息数据 (Encrypt message data)
	encData, err := suite.encryptionSvc.EncryptData([]byte("sensitive data"))
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}
	
	// 验证加密数据可以解密 (Verify encrypted data can be decrypted)
	decrypted, err := suite.encryptionSvc.DecryptData(encData)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}
	
	if string(decrypted) != "sensitive data" {
		t.Errorf("Decrypted data mismatch. Expected: 'sensitive data', Got: '%s'", string(decrypted))
	}
	
	// 发送可靠消息 (Send reliable message)
	ctx := context.Background()
	err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}
	
	// 等待消息发送 (Wait for message to be sent)
	time.Sleep(50 * time.Millisecond)
	
	// 验证消息已发送 (Verify message was sent)
	stats := suite.reliabilityMgr.GetStats()
	pendingCount, ok := stats["pending_count"].(int)
	if !ok || pendingCount != 1 {
		t.Errorf("Expected 1 pending message, got %v", stats["pending_count"])
	}
	
	// 处理消息确认 (Handle message acknowledgment)
	err = suite.reliabilityMgr.HandleAck(msg.ID)
	if err != nil {
		t.Fatalf("Failed to handle ack: %v", err)
	}
	
	// 验证消息状态 (Verify message status)
	pendingMsg, exists := suite.reliabilityMgr.GetPendingMessage(msg.ID)
	if !exists {
		t.Error("Message should still be in pending list")
	}
	if pendingMsg.Status != protocol.MessageStatusAcked {
		t.Errorf("Expected status acked, got %s", pendingMsg.Status)
	}
	
	t.Logf("✅ Secure message flow test completed successfully")
}

// TestReplayAttackProtection 测试重放攻击防护 (Tests replay attack protection)
func TestReplayAttackProtection(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()
	
	// 创建测试消息 (Create test message)
	msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})
	
	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetSequenceNumber()
	msg.GenerateAndSetChecksum()
	
	// 第一次发送应该成功 (First send should succeed)
	ctx := context.Background()
	err := suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("First send should succeed: %v", err)
	}
	
	// 等待处理 (Wait for processing)
	time.Sleep(50 * time.Millisecond)
	
	// 重放攻击：尝试再次发送相同消息 (Replay attack: try to send same message again)
	err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err == nil {
		t.Error("Replay attack should be detected and blocked")
	}
	
	// 验证重放攻击防护 (Verify replay attack protection)
	nonce := msg.ID + msg.From + msg.To
	isReplay := suite.replayProtector.IsReplay(nonce, msg.Timestamp)
	if !isReplay {
		// 第一次检查，记录nonce (First check, record nonce)
		suite.replayProtector.RecordNonce(nonce, msg.Timestamp)
	}
	
	// 第二次检查应该检测到重放 (Second check should detect replay)
	isReplay = suite.replayProtector.IsReplay(nonce, msg.Timestamp)
	if !isReplay {
		t.Error("Replay protector should detect replay attack")
	}
	
	t.Logf("✅ Replay attack protection test completed successfully")
}

// TestKeyRotationDuringOperation 测试运行期间的密钥轮换 (Tests key rotation during operation)
func TestKeyRotationDuringOperation(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()
	
	// 记录初始密钥版本 (Record initial key version)
	initialVersion := suite.keyManager.GetKeyVersion()
	
	// 加密一些数据 (Encrypt some data)
	testData := []byte("data before key rotation")
	encData1, err := suite.encryptionSvc.EncryptData(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}
	
	// 等待密钥自动轮换 (Wait for automatic key rotation)
	time.Sleep(150 * time.Millisecond)
	
	// 验证密钥已轮换 (Verify key has been rotated)
	newVersion := suite.keyManager.GetKeyVersion()
	if newVersion <= initialVersion {
		t.Errorf("Key should have been rotated. Initial: %d, Current: %d", initialVersion, newVersion)
	}
	
	// 使用新密钥加密数据 (Encrypt data with new key)
	newTestData := []byte("data after key rotation")
	encData2, err := suite.encryptionSvc.EncryptData(newTestData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with new key: %v", err)
	}
	
	// 验证新密钥版本不同 (Verify new key version is different)
	if encData2.KeyVersion <= encData1.KeyVersion {
		t.Errorf("New encryption should use newer key version. Old: %d, New: %d", 
			encData1.KeyVersion, encData2.KeyVersion)
	}
	
	// 验证旧数据仍能解密 (Verify old data can still be decrypted)
	decrypted1, err := suite.encryptionSvc.DecryptData(encData1)
	if err != nil {
		t.Fatalf("Failed to decrypt old data: %v", err)
	}
	if string(decrypted1) != string(testData) {
		t.Errorf("Old data decryption failed. Expected: %s, Got: %s", testData, decrypted1)
	}
	
	// 验证新数据能正确解密 (Verify new data can be decrypted correctly)
	decrypted2, err := suite.encryptionSvc.DecryptData(encData2)
	if err != nil {
		t.Fatalf("Failed to decrypt new data: %v", err)
	}
	if string(decrypted2) != string(newTestData) {
		t.Errorf("New data decryption failed. Expected: %s, Got: %s", newTestData, decrypted2)
	}
	
	t.Logf("✅ Key rotation during operation test completed successfully")
}
