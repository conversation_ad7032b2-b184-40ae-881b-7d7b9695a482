package security

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"server-monitor/internal/config"
	"server-monitor/internal/crypto"
	"server-monitor/internal/protocol"
)

// SecurityTestSuite 安全特性集成测试套件 (Security feature integration test suite)
type SecurityTestSuite struct {
	config          *config.Config
	keyManager      *crypto.KeyManager
	encryptionSvc   *crypto.EncryptionService
	reliabilityMgr  *protocol.MessageReliabilityManager
	validator       *protocol.MessageValidator
	replayProtector *crypto.ReplayProtector
}

// NewSecurityTestSuite 创建新的安全测试套件 (Creates a new security test suite)
func NewSecurityTestSuite(t *testing.T) *SecurityTestSuite {
	// 创建测试配置 (Create test configuration)
	manager := config.NewManager("")
	err := manager.Load()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	cfg := manager.Get()

	// 设置测试用的安全配置 (Set security config for testing)
	cfg.Security.KeyManager.MasterPassword = "test-master-password-for-integration"
	cfg.Security.KeyManager.RotationInterval = 100 * time.Millisecond // 快速轮换用于测试 (Fast rotation for testing)
	cfg.Security.KeyManager.KeyTTL = time.Hour
	cfg.Security.KeyManager.AutoRotate = true

	// 创建密钥管理器 (Create key manager)
	kmConfig := &crypto.KeyManagerConfig{
		RotationInterval: cfg.Security.KeyManager.RotationInterval,
		KeyTTL:           cfg.Security.KeyManager.KeyTTL,
		MaxKeyHistory:    cfg.Security.KeyManager.MaxKeyHistory,
		AutoRotate:       cfg.Security.KeyManager.AutoRotate,
		MasterPassword:   []byte(cfg.Security.KeyManager.MasterPassword),
	}

	keyManager, err := crypto.NewKeyManager(kmConfig)
	if err != nil {
		t.Fatalf("Failed to create key manager: %v", err)
	}

	// 创建加密服务 (Create encryption service)
	encryptionSvc := crypto.NewEncryptionService(keyManager)

	// 创建消息验证器 (Create message validator)
	validatorConfig := protocol.DefaultValidatorConfig()
	validator := protocol.NewMessageValidator(validatorConfig)

	// 创建重放攻击防护器 (Create replay protector)
	replayConfig := &crypto.ReplayProtectorConfig{
		TimeWindow:         5 * time.Minute,
		CacheSize:          1000,
		ClockSkewTolerance: 30 * time.Second,
		EnableCleanup:      true,
		CleanupInterval:    time.Minute,
	}
	replayProtector, err := crypto.NewReplayProtector(replayConfig)
	if err != nil {
		t.Fatalf("Failed to create replay protector: %v", err)
	}

	// 创建模拟消息发送器 (Create mock message sender)
	mockSender := &MockSecureMessageSender{
		encryptionSvc: encryptionSvc,
		validator:     validator,
	}

	// 创建消息可靠性管理器 (Create message reliability manager)
	reliabilityConfig := protocol.DefaultMessageReliabilityConfig()
	reliabilityConfig.AckTimeout = 100 * time.Millisecond // 短超时用于测试 (Short timeout for testing)
	reliabilityConfig.MaxPendingMsgs = 10000 // 增加待处理消息限制用于测试 (Increase pending message limit for testing)
	reliabilityMgr := protocol.NewMessageReliabilityManager(reliabilityConfig, mockSender)

	return &SecurityTestSuite{
		config:          cfg,
		keyManager:      keyManager,
		encryptionSvc:   encryptionSvc,
		reliabilityMgr:  reliabilityMgr,
		validator:       validator,
		replayProtector: replayProtector,
	}
}

// Cleanup 清理测试资源 (Cleanup test resources)
func (s *SecurityTestSuite) Cleanup() {
	if s.keyManager != nil {
		s.keyManager.Stop()
	}
	if s.reliabilityMgr != nil {
		s.reliabilityMgr.Stop()
	}
}

// MockSecureMessageSender 模拟安全消息发送器 (Mock secure message sender)
type MockSecureMessageSender struct {
	encryptionSvc *crypto.EncryptionService
	validator     *protocol.MessageValidator
	sentMessages  []string
	mutex         sync.Mutex
}

// SendMessage 发送消息 (Sends message)
func (m *MockSecureMessageSender) SendMessage(ctx context.Context, msg *protocol.ApplicationMessage) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 验证消息 (Validate message)
	if err := m.validator.ValidateMessage(msg); err != nil {
		return err
	}

	// 模拟发送 (Simulate sending)
	m.sentMessages = append(m.sentMessages, msg.ID)
	return nil
}

// GetSentMessages 获取已发送消息 (Gets sent messages)
func (m *MockSecureMessageSender) GetSentMessages() []string {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	result := make([]string, len(m.sentMessages))
	copy(result, m.sentMessages)
	return result
}

// TestSecureMessageFlow 测试完整的安全消息流程 (Tests complete secure message flow)
func TestSecureMessageFlow(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()

	// 创建测试消息 (Create test message)
	msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	// 设置时间戳 (Set timestamp)
	msg.Timestamp = time.Now().UnixNano()

	// 生成序列号 (Generate sequence number)
	msg.GenerateAndSetSequenceNumber()

	// 生成校验和 (Generate checksum)
	msg.GenerateAndSetChecksum()

	// 验证消息完整性 (Validate message integrity)
	err := msg.ValidateWithIntegrity()
	if err != nil {
		t.Fatalf("Message integrity validation failed: %v", err)
	}

	// 加密消息数据 (Encrypt message data)
	encData, err := suite.encryptionSvc.EncryptData([]byte("sensitive data"))
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	// 验证加密数据可以解密 (Verify encrypted data can be decrypted)
	decrypted, err := suite.encryptionSvc.DecryptData(encData)
	if err != nil {
		t.Fatalf("Failed to decrypt data: %v", err)
	}

	if string(decrypted) != "sensitive data" {
		t.Errorf("Decrypted data mismatch. Expected: 'sensitive data', Got: '%s'", string(decrypted))
	}

	// 发送可靠消息 (Send reliable message)
	ctx := context.Background()
	err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("Failed to send reliable message: %v", err)
	}

	// 等待消息发送 (Wait for message to be sent)
	time.Sleep(50 * time.Millisecond)

	// 验证消息已发送 (Verify message was sent)
	stats := suite.reliabilityMgr.GetStats()
	pendingCount, ok := stats["pending_count"].(int)
	if !ok || pendingCount != 1 {
		t.Errorf("Expected 1 pending message, got %v", stats["pending_count"])
	}

	// 处理消息确认 (Handle message acknowledgment)
	err = suite.reliabilityMgr.HandleAck(msg.ID)
	if err != nil {
		t.Fatalf("Failed to handle ack: %v", err)
	}

	// 验证消息状态 (Verify message status)
	pendingMsg, exists := suite.reliabilityMgr.GetPendingMessage(msg.ID)
	if !exists {
		t.Error("Message should still be in pending list")
	}
	if pendingMsg.Status != protocol.MessageStatusAcked {
		t.Errorf("Expected status acked, got %s", pendingMsg.Status)
	}

	t.Logf("✅ Secure message flow test completed successfully")
}

// TestReplayAttackProtection 测试重放攻击防护 (Tests replay attack protection)
func TestReplayAttackProtection(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()

	// 创建测试消息 (Create test message)
	msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
		Username: "testuser",
		Password: "testpass",
		ClientID: "client_001",
		Version:  "1.0.0",
	})

	msg.Timestamp = time.Now().UnixNano()
	msg.GenerateAndSetSequenceNumber()
	msg.GenerateAndSetChecksum()

	// 第一次发送应该成功 (First send should succeed)
	ctx := context.Background()
	err := suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err != nil {
		t.Fatalf("First send should succeed: %v", err)
	}

	// 等待处理 (Wait for processing)
	time.Sleep(50 * time.Millisecond)

	// 重放攻击：尝试再次发送相同消息 (Replay attack: try to send same message again)
	err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
	if err == nil {
		t.Error("Replay attack should be detected and blocked")
	}

	// 验证重放攻击防护 (Verify replay attack protection)
	// 第一次验证应该成功 (First validation should succeed)
	err = suite.replayProtector.ValidateMessage(msg.ID, msg.Timestamp)
	if err != nil {
		t.Fatalf("First message validation should succeed: %v", err)
	}

	// 第二次验证应该检测到重放攻击 (Second validation should detect replay attack)
	err = suite.replayProtector.ValidateMessage(msg.ID, msg.Timestamp)
	if err == nil {
		t.Error("Replay protector should detect replay attack")
	} else if !strings.Contains(err.Error(), "replay attack detected") {
		t.Errorf("Expected replay attack error, got: %v", err)
	}

	t.Logf("✅ Replay attack protection test completed successfully")
}

// TestKeyRotationDuringOperation 测试运行期间的密钥轮换 (Tests key rotation during operation)
func TestKeyRotationDuringOperation(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()

	// 记录初始密钥版本 (Record initial key version)
	initialVersion := suite.keyManager.GetKeyVersion()

	// 加密一些数据 (Encrypt some data)
	testData := []byte("data before key rotation")
	encData1, err := suite.encryptionSvc.EncryptData(testData)
	if err != nil {
		t.Fatalf("Failed to encrypt data: %v", err)
	}

	// 等待密钥自动轮换 (Wait for automatic key rotation)
	time.Sleep(150 * time.Millisecond)

	// 验证密钥已轮换 (Verify key has been rotated)
	newVersion := suite.keyManager.GetKeyVersion()
	if newVersion <= initialVersion {
		t.Errorf("Key should have been rotated. Initial: %d, Current: %d", initialVersion, newVersion)
	}

	// 使用新密钥加密数据 (Encrypt data with new key)
	newTestData := []byte("data after key rotation")
	encData2, err := suite.encryptionSvc.EncryptData(newTestData)
	if err != nil {
		t.Fatalf("Failed to encrypt data with new key: %v", err)
	}

	// 验证新密钥版本不同 (Verify new key version is different)
	if encData2.KeyVersion <= encData1.KeyVersion {
		t.Errorf("New encryption should use newer key version. Old: %d, New: %d",
			encData1.KeyVersion, encData2.KeyVersion)
	}

	// 验证旧数据仍能解密 (Verify old data can still be decrypted)
	decrypted1, err := suite.encryptionSvc.DecryptData(encData1)
	if err != nil {
		t.Fatalf("Failed to decrypt old data: %v", err)
	}
	if string(decrypted1) != string(testData) {
		t.Errorf("Old data decryption failed. Expected: %s, Got: %s", testData, decrypted1)
	}

	// 验证新数据能正确解密 (Verify new data can be decrypted correctly)
	decrypted2, err := suite.encryptionSvc.DecryptData(encData2)
	if err != nil {
		t.Fatalf("Failed to decrypt new data: %v", err)
	}
	if string(decrypted2) != string(newTestData) {
		t.Errorf("New data decryption failed. Expected: %s, Got: %s", newTestData, decrypted2)
	}

	t.Logf("✅ Key rotation during operation test completed successfully")
}

// TestConcurrentSecureOperations 测试并发安全操作 (Tests concurrent secure operations)
func TestConcurrentSecureOperations(t *testing.T) {
	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()

	const numGoroutines = 10
	const messagesPerGoroutine = 5

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*messagesPerGoroutine)

	// 启动多个goroutine并发发送消息 (Start multiple goroutines to send messages concurrently)
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < messagesPerGoroutine; j++ {
				// 创建唯一消息 (Create unique message)
				msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth,
					fmt.Sprintf("client%d", goroutineID),
					"server1",
					protocol.AuthData{
						Username: fmt.Sprintf("user%d_%d", goroutineID, j),
						Password: "testpass",
						ClientID: fmt.Sprintf("client_%d_%d", goroutineID, j),
						Version:  "1.0.0",
					})

				msg.Timestamp = time.Now().UnixNano()
				msg.GenerateAndSetSequenceNumber()
				msg.GenerateAndSetChecksum()

				// 加密数据 (Encrypt data)
				testData := fmt.Sprintf("concurrent data %d_%d", goroutineID, j)
				encData, err := suite.encryptionSvc.EncryptData([]byte(testData))
				if err != nil {
					errors <- fmt.Errorf("goroutine %d, message %d: encryption failed: %v", goroutineID, j, err)
					continue
				}

				// 验证解密 (Verify decryption)
				decrypted, err := suite.encryptionSvc.DecryptData(encData)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d, message %d: decryption failed: %v", goroutineID, j, err)
					continue
				}

				if string(decrypted) != testData {
					errors <- fmt.Errorf("goroutine %d, message %d: data mismatch", goroutineID, j)
					continue
				}

				// 发送可靠消息 (Send reliable message)
				ctx := context.Background()
				err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d, message %d: send failed: %v", goroutineID, j, err)
					continue
				}
			}
		}(i)
	}

	// 等待所有goroutine完成 (Wait for all goroutines to complete)
	wg.Wait()
	close(errors)

	// 检查错误 (Check for errors)
	var errorList []error
	for err := range errors {
		errorList = append(errorList, err)
	}

	if len(errorList) > 0 {
		t.Errorf("Concurrent operations failed with %d errors:", len(errorList))
		for _, err := range errorList {
			t.Errorf("  - %v", err)
		}
	}

	// 等待消息处理 (Wait for message processing)
	time.Sleep(200 * time.Millisecond)

	// 验证统计信息 (Verify statistics)
	stats := suite.reliabilityMgr.GetStats()
	expectedMessages := numGoroutines * messagesPerGoroutine
	pendingCount, ok := stats["pending_count"].(int)
	if !ok {
		t.Error("Failed to get pending count from stats")
	} else if pendingCount != expectedMessages {
		t.Errorf("Expected %d pending messages, got %d", expectedMessages, pendingCount)
	}

	t.Logf("✅ Concurrent secure operations test completed successfully with %d messages", expectedMessages)
}

// TestLongRunningStability 测试长时间运行稳定性 (Tests long-running stability)
func TestLongRunningStability(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping long-running stability test in short mode")
	}

	suite := NewSecurityTestSuite(t)
	defer suite.Cleanup()

	// 运行5分钟的稳定性测试 (Run 5-minute stability test)
	duration := 30 * time.Second // 缩短到30秒用于CI (Shortened to 30s for CI)
	if testing.Verbose() {
		duration = 5 * time.Minute // 详细模式下运行更长时间 (Longer duration in verbose mode)
	}

	startTime := time.Now()
	messageCount := 0
	errorCount := 0

	t.Logf("Starting stability test for %v", duration)

	for time.Since(startTime) < duration {
		// 创建测试消息 (Create test message)
		msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
			Username: fmt.Sprintf("user_%d", messageCount),
			Password: "testpass",
			ClientID: fmt.Sprintf("client_%d", messageCount),
			Version:  "1.0.0",
		})

		msg.Timestamp = time.Now().UnixNano()
		msg.GenerateAndSetSequenceNumber()
		msg.GenerateAndSetChecksum()

		// 加密和解密测试 (Encryption and decryption test)
		testData := fmt.Sprintf("stability test data %d", messageCount)
		encData, err := suite.encryptionSvc.EncryptData([]byte(testData))
		if err != nil {
			errorCount++
			t.Logf("Encryption error at message %d: %v", messageCount, err)
			continue
		}

		decrypted, err := suite.encryptionSvc.DecryptData(encData)
		if err != nil {
			errorCount++
			t.Logf("Decryption error at message %d: %v", messageCount, err)
			continue
		}

		if string(decrypted) != testData {
			errorCount++
			t.Logf("Data mismatch at message %d", messageCount)
			continue
		}

		// 发送消息测试 (Send message test)
		ctx := context.Background()
		err = suite.reliabilityMgr.SendReliableMessage(ctx, msg)
		if err != nil {
			errorCount++
			t.Logf("Send error at message %d: %v", messageCount, err)
			continue
		}

		messageCount++

		// 每100条消息检查一次统计 (Check stats every 100 messages)
		if messageCount%100 == 0 {
			stats := suite.reliabilityMgr.GetStats()
			keyStats := suite.keyManager.GetKeyStats()
			t.Logf("Progress: %d messages, %d errors, pending: %v, current key: v%v",
				messageCount, errorCount, stats["pending_count"], keyStats["current_key_version"])
		}

		// 短暂休息避免过度消耗资源 (Brief pause to avoid excessive resource consumption)
		time.Sleep(time.Millisecond)
	}

	// 最终统计 (Final statistics)
	stats := suite.reliabilityMgr.GetStats()
	keyStats := suite.keyManager.GetKeyStats()

	t.Logf("✅ Stability test completed:")
	t.Logf("  Duration: %v", time.Since(startTime))
	t.Logf("  Messages processed: %d", messageCount)
	t.Logf("  Errors: %d (%.2f%%)", errorCount, float64(errorCount)/float64(messageCount)*100)
	t.Logf("  Pending messages: %v", stats["pending_count"])
	t.Logf("  Key rotations: %v", keyStats["current_key_version"])
	t.Logf("  Historical keys: %v", keyStats["historical_keys_count"])

	// 验证错误率在可接受范围内 (Verify error rate is within acceptable range)
	errorRate := float64(errorCount) / float64(messageCount)
	if errorRate > 0.01 { // 1% 错误率阈值 (1% error rate threshold)
		t.Errorf("Error rate too high: %.2f%% (threshold: 1%%)", errorRate*100)
	}
}

// BenchmarkSecureMessageFlow 安全消息流程性能基准测试 (Secure message flow performance benchmark)
func BenchmarkSecureMessageFlow(b *testing.B) {
	// 创建测试套件 (Create test suite)
	manager := config.NewManager("")
	err := manager.Load()
	if err != nil {
		b.Fatalf("Failed to load config: %v", err)
	}
	cfg := manager.Get()
	cfg.Security.KeyManager.MasterPassword = "benchmark-master-password"
	cfg.Security.KeyManager.AutoRotate = false // 禁用自动轮换以获得一致的基准测试结果 (Disable auto rotation for consistent benchmark results)

	// 创建密钥管理器 (Create key manager)
	kmConfig := &crypto.KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    10,
		AutoRotate:       false,
		MasterPassword:   []byte(cfg.Security.KeyManager.MasterPassword),
	}

	keyManager, err := crypto.NewKeyManager(kmConfig)
	if err != nil {
		b.Fatalf("Failed to create key manager: %v", err)
	}
	defer keyManager.Stop()

	encryptionSvc := crypto.NewEncryptionService(keyManager)
	validator := protocol.NewMessageValidator(protocol.DefaultValidatorConfig())

	// 预创建测试消息 (Pre-create test messages)
	messages := make([]*protocol.ApplicationMessage, b.N)
	for i := 0; i < b.N; i++ {
		msg := protocol.NewApplicationMessage(protocol.MessageTypeAuth, "client1", "server1", protocol.AuthData{
			Username: fmt.Sprintf("user_%d", i),
			Password: "testpass",
			ClientID: fmt.Sprintf("client_%d", i),
			Version:  "1.0.0",
		})
		msg.Timestamp = time.Now().UnixNano()
		msg.GenerateAndSetSequenceNumber()
		msg.GenerateAndSetChecksum()
		messages[i] = msg
	}

	b.ResetTimer()
	b.ReportAllocs()

	// 基准测试：完整的安全消息流程 (Benchmark: complete secure message flow)
	for i := 0; i < b.N; i++ {
		msg := messages[i]

		// 验证消息完整性 (Validate message integrity)
		err := validator.ValidateMessage(msg)
		if err != nil {
			b.Fatalf("Message validation failed: %v", err)
		}

		// 加密数据 (Encrypt data)
		testData := fmt.Sprintf("benchmark data %d", i)
		encData, err := encryptionSvc.EncryptData([]byte(testData))
		if err != nil {
			b.Fatalf("Encryption failed: %v", err)
		}

		// 解密数据 (Decrypt data)
		_, err = encryptionSvc.DecryptData(encData)
		if err != nil {
			b.Fatalf("Decryption failed: %v", err)
		}
	}
}

// BenchmarkEncryptionOnly 仅加密性能基准测试 (Encryption-only performance benchmark)
func BenchmarkEncryptionOnly(b *testing.B) {
	manager := config.NewManager("")
	err := manager.Load()
	if err != nil {
		b.Fatalf("Failed to load config: %v", err)
	}
	cfg := manager.Get()
	cfg.Security.KeyManager.MasterPassword = "benchmark-master-password"

	kmConfig := &crypto.KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    10,
		AutoRotate:       false,
		MasterPassword:   []byte(cfg.Security.KeyManager.MasterPassword),
	}

	keyManager, err := crypto.NewKeyManager(kmConfig)
	if err != nil {
		b.Fatalf("Failed to create key manager: %v", err)
	}
	defer keyManager.Stop()

	encryptionSvc := crypto.NewEncryptionService(keyManager)
	testData := []byte("benchmark encryption data")

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := encryptionSvc.EncryptData(testData)
		if err != nil {
			b.Fatalf("Encryption failed: %v", err)
		}
	}
}

// BenchmarkDecryptionOnly 仅解密性能基准测试 (Decryption-only performance benchmark)
func BenchmarkDecryptionOnly(b *testing.B) {
	manager := config.NewManager("")
	err := manager.Load()
	if err != nil {
		b.Fatalf("Failed to load config: %v", err)
	}
	cfg := manager.Get()
	cfg.Security.KeyManager.MasterPassword = "benchmark-master-password"

	kmConfig := &crypto.KeyManagerConfig{
		RotationInterval: time.Hour,
		KeyTTL:           24 * time.Hour,
		MaxKeyHistory:    10,
		AutoRotate:       false,
		MasterPassword:   []byte(cfg.Security.KeyManager.MasterPassword),
	}

	keyManager, err := crypto.NewKeyManager(kmConfig)
	if err != nil {
		b.Fatalf("Failed to create key manager: %v", err)
	}
	defer keyManager.Stop()

	encryptionSvc := crypto.NewEncryptionService(keyManager)
	testData := []byte("benchmark decryption data")

	// 预加密数据 (Pre-encrypt data)
	encData, err := encryptionSvc.EncryptData(testData)
	if err != nil {
		b.Fatalf("Failed to encrypt test data: %v", err)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := encryptionSvc.DecryptData(encData)
		if err != nil {
			b.Fatalf("Decryption failed: %v", err)
		}
	}
}
