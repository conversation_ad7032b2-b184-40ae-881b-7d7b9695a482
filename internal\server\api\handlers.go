package api

import (
	"net/http"
	"strconv"
	"time" // Add time package

	"github.com/gin-gonic/gin"
	"server-monitor/internal/database"
	"server-monitor/internal/models"
)

// ServerRepository 接口定义了与服务器数据交互的方法
type ServerRepository interface {
	CreateServer(server *models.Server) error
	GetServer(id int) (*models.Server, error)
	UpdateServer(server *models.Server) error
	DeleteServer(id int) error
	ListServers(active bool) ([]*models.Server, error)
}

// CreateServerRequest 定义了创建服务器的请求体
type CreateServerRequest struct {
	Name        string `json:"name" binding:"required"`
	IPAddress   string `json:"ip_address" binding:"required"`
	Hostname    string `json:"hostname"`
	OSType      string `json:"os_type"`
	OSVersion   string `json:"os_version"`
	Description string `json:"description"`
	Tags        string `json:"tags"`
}

// UpdateServerRequest 定义了更新服务器的请求体
type UpdateServerRequest struct {
	ID          uint   `json:"id" binding:"required"`
	Name        string `json:"name"`
	IPAddress   string `json:"ip_address"`
	Hostname    string `json:"hostname"`
	OSType      string `json:"os_type"`
	OSVersion   string `json:"os_version"`
	IsActive    *bool  `json:"is_active"` // 使用指针以区分未提供和明确设置为false
	Description string `json:"description"`
	Tags        string `json:"tags"`
}

// CreateServerHandler 处理创建服务器的请求
func CreateServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req CreateServerRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		server := &models.Server{
			Name:        req.Name,
			IPAddress:   req.IPAddress,
			Hostname:    req.Hostname,
			OSType:      req.OSType,
			OSVersion:   req.OSVersion,
			IsActive:    true, // 默认激活
			Description: req.Description,
			Tags:        req.Tags,
		}

		if err := repo.CreateServer(server); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, server)
	}
}

// GetServerHandler 处理获取单个服务器的请求
func GetServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := strconv.Atoi(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
			return
		}

		server, err := repo.GetServer(id)
		if err != nil {
			if err == database.ErrRecordNotFound {
				c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, server)
	}
}

// UpdateServerHandler 处理更新服务器的请求
func UpdateServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req UpdateServerRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		server, err := repo.GetServer(int(req.ID))
		if err != nil {
			if err == database.ErrRecordNotFound {
				c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// 更新字段
		if req.Name != "" {
			server.Name = req.Name
		}
		if req.IPAddress != "" {
			server.IPAddress = req.IPAddress
		}
		if req.Hostname != "" {
			server.Hostname = req.Hostname
		}
		if req.OSType != "" {
			server.OSType = req.OSType
		}
		if req.OSVersion != "" {
			server.OSVersion = req.OSVersion
		}
		if req.IsActive != nil { // 检查是否提供了 IsActive 字段
			server.IsActive = *req.IsActive
		}
		if req.Description != "" {
			server.Description = req.Description
		}
		if req.Tags != "" {
			server.Tags = req.Tags
		}

		if err := repo.UpdateServer(server); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, server)
	}
}

// DeleteServerHandler 处理删除服务器的请求
func DeleteServerHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := strconv.Atoi(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
			return
		}

		if err := repo.DeleteServer(id); err != nil {
			if err == database.ErrRecordNotFound {
				c.JSON(http.StatusNotFound, gin.H{"error": "Server not found"})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusNoContent, nil) // 204 No Content
	}
}

// GetSystemInfoHandler 处理获取系统信息的请求
func GetSystemInfoHandler(repo ServerRepository) gin.HandlerFunc { // Note: Using ServerRepository for now, will need to adjust if a more generic repo is introduced
	return func(c *gin.Context) {
		serverIDParam := c.Param("serverID")
		serverID, err := strconv.ParseUint(serverIDParam, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid server ID"})
			return
		}

		startTimeStr := c.Query("start_time")
		endTimeStr := c.Query("end_time")

		var startTime, endTime time.Time

		if startTimeStr != "" {
			startTime, err = time.Parse(time.RFC3339, startTimeStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_time format. Use RFC3339."})
				return
			}
		}

		if endTimeStr != "" {
			endTime, err = time.Parse(time.RFC3339, endTimeStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_time format. Use RFC3339."})
				return
			}
		}

		// Need to cast repo to database.Repository to access GetSystemInfo
		dbRepo, ok := repo.(database.Repository)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Repository does not support SystemInfo operations"})
			return
		}

		systemInfos, err := dbRepo.GetSystemInfo(uint(serverID), startTime, endTime)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, systemInfos)
	}
}

// ListServersHandler 处理列出所有服务器的请求
func ListServersHandler(repo ServerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 可以添加查询参数来过滤，例如 active=true/false
		activeParam := c.DefaultQuery("active", "true")
		active := true
		if activeParam == "false" {
			active = false
		}

		servers, err := repo.ListServers(active)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, servers)
	}
}

// HealthCheckHandler 处理健康检查端点。(HealthCheckHandler handles the health check endpoint.)
func HealthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// ExampleRequest 表示示例端点的请求体。(ExampleRequest represents the request body for the example endpoint.)
type ExampleRequest struct {
	Name  string `json:"name" binding:"required"`
	Value int    `json:"value" binding:"required,gte=0"`
}

// ExampleHandler 处理带有数据验证的示例端点。(ExampleHandler handles the example endpoint with data validation.)
func ExampleHandler(c *gin.Context) {
	var req ExampleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data received and validated successfully", "data": req})
}
